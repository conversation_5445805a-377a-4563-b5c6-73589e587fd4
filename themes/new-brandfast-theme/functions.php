<?php

declare(strict_types=1);

use src\fortnox\api\WF_Request;

//---------------------------------------------------------------------------------
//  Admin bar in footer
//---------------------------------------------------------------------------------
function fb_move_admin_bar()
{
	if (is_user_logged_in()) {
		echo '
        <style type="text/css">
            body {
            margin-top: -32px;
            padding-bottom: 32px;
            }
            body.admin-bar #wphead {
               padding-top: 0;
            }
            #wpadminbar {
                top: auto !important;
                bottom: 0;
            }
            #wpadminbar .quicklinks .menupop .ab-sub-wrapper {
                bottom: 32px;
            }
        @media screen and (max-width: 782px) {
            body {
                margin-top: -46px;
                padding-bottom: 46px;
            }
        }
        </style>';
	}
}
add_action('wp_head', 'fb_move_admin_bar', 100);

function brandfast_setup_theme_defaults()
{
    /*
     * Make theme available for translation.
     * Translations can be filed in the /languages/ directory.
     */
    load_theme_textdomain('brandfast', get_template_directory() . '/languages');
    load_textdomain('vistrom', get_template_directory() . '/languages/vistrom-sv_SE.mo');

    // Show the admin bar.
    // show_admin_bar(false);

    // Add various theme supports
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');

    // Add HTML5 support.
    add_theme_support(
        'html5',
        [
            'caption',
            'comment-form',
            'comment-list',
            'gallery',
            'search-form',
            'widgets',
        ]
    );

    // Register nav menus
    register_nav_menus([
        'primary-menu' => esc_html__('Primary Menu', 'brandfast'),
        'primary-mobile-menu' => esc_html__('Primary Mobile Menu', 'brandfast'),
        'secondary-menu' => esc_html__('Secondary Menu', 'brandfast'),
        'footer' => esc_html__('Footer', 'brandfast'),
        'courses' => esc_html__('Courses', 'brandfast'),
        'product-cats' => esc_html__('Product Categories', 'brandfast'),
    ]);
}
add_action('after_setup_theme', 'brandfast_setup_theme_defaults');

// Enqueue and register scripts the right way.
function brandfast_enqueue_scripts()
{
    wp_deregister_script('jquery');

    wp_register_script('jquery', '//code.jquery.com/jquery-2.2.4.min.js', '', '', true);
    wp_enqueue_script('jquery');

    wp_enqueue_script('googlemaps', 'https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places&signed_in=true&key=AIzaSyDu1Cpeb0AaSw99ZtEIqTk3DhDIuoUawFI');

    if (is_page_template('templates/template-contact.php')) {
        wp_enqueue_script('maps', get_template_directory_uri() . '/assets/scripts/map.js', array('jquery', 'googlemaps'), null, true);
    }

    if (is_checkout()) {
        wp_enqueue_script('brandfast-checkout-cart-quantity', get_template_directory_uri() . '/assets/scripts/wc-ajax/checkout-cart-quantity.js', ['jquery'], null, true);
        wp_enqueue_script('brandfast-checkout-customer-type', get_template_directory_uri() . '/assets/scripts/wc-ajax/checkout-customer-type.js', ['jquery'], null, true);
    }

    wp_enqueue_script(
        'vistrom-script',
        get_stylesheet_directory_uri() . '/assets/scripts/app.js',
        ["wp-i18n"],
        filemtime(get_stylesheet_directory() . '/assets/scripts/app.js'),
        true
    );

    // E-commerce base / Strom package translations.
    wp_set_script_translations("vistrom-script", "vistrom", get_stylesheet_directory() . "/languages");

    wp_register_script('brandfast-swiper', get_template_directory_uri() . '/assets/scripts/swiper.js', [], filemtime(get_template_directory() . '/assets/scripts/swiper.js'), true);
    wp_enqueue_script('brandfast-swiper');

    wp_enqueue_style('fira-sans', 'https://fonts.googleapis.com/css2?family=Fira+Sans:ital,wght@0,400;0,500;0,600;0,700;1,400;1,700&display=swap');

    /**
     * Enqueue Gutenberg plugin's styles because it doesn't happen automatically for some reason.
     *
     * TODO: Find out why this is necessary.
     */
    wp_enqueue_style('gutenberg', plugin_dir_url('gutenberg') . '/gutenberg/build/block-library/style.css', [], null, false);

    wp_enqueue_style('brandfast', get_template_directory_uri() . '/assets/styles/app.css', [], filemtime(get_template_directory() . '/assets/styles/app.css'), false);
}
add_action('wp_enqueue_scripts', 'brandfast_enqueue_scripts');

function brandfast_enqueue_admin_scripts()
{
    wp_deregister_script('wpuxss-eml-media-views-script');

    wp_enqueue_script(
        'wpuxss-eml-media-views-script',
        get_template_directory_uri() . '/assets/scripts/eml-media-views--fixed.js',
        array('media-views'),
        '',
        true
    );
}
add_action('admin_enqueue_scripts', 'brandfast_enqueue_admin_scripts', 100);

// Remove JPEG compression.
add_filter('jpeg_quality', function () {
    return 100;
}, 10, 2);

//Function to highlight menuitems when on single post of CPT
require get_theme_file_path('includes/extra_classes_cpt_menuitem.php');

require get_theme_file_path('includes/friendly_mime.php');

//Alter login page styles
require get_theme_file_path('includes/wp-login-styles.php');

//Alter admin backend styles
require get_theme_file_path('includes/wp-admin-styles.php');

//Add excerpt to pages and modify excerpt filter
require get_theme_file_path('includes/custom_excerpt.php');

//Our custom nav walker class
require get_theme_file_path('includes/custom-nav-walker.php');

//Change Woocommerce defaults
require get_theme_file_path('includes/changes-to-woocommerce.php');

// Add options pages
require get_theme_file_path('includes/acf-options-page.php');

// Add plugin integrations
require get_theme_file_path('includes/rank-math-schema.php');

// ACF Local Json
require_once get_theme_file_path('library/acf-local-json.php');

/// ------ THE ORDER OF THESE ARE IMPORTANT -------
// https://github.com/johnbillion/extended-cpts
require_once get_theme_file_path('library/extended-cpts.php');

// https://github.com/johnbillion/extended-taxos
require_once get_theme_file_path('library/extended-taxos.php');

// Register custom post types.
require get_theme_file_path('post-types/articles.php');
require get_theme_file_path('post-types/courses.php');

// Numeric posts navigation
require get_theme_file_path('includes/numeric-posts-navigation.php');
include_once('library/video-helper.php');

// Customize Gutenberg.
require get_theme_file_path('includes/gutenberg.php');

// Helpers.
require get_theme_file_path('includes/helpers.php');

// Exclude hidden products from search results
function exclude_hidden_products_from_search($query)
{
    if (!is_admin() && $query->is_search) {
        $product_visibility_term_ids = wc_get_product_visibility_term_ids();

        $query->set('tax_query', [
            [
                'taxonomy' => 'product_visibility',
                'field'    => 'term_taxonomy_id',
                'terms'    => $product_visibility_term_ids['exclude-from-search'],
                'operator' => 'NOT IN',
            ]
        ]);
    }
}
add_action('pre_get_posts', 'exclude_hidden_products_from_search');

function isa_add_cron_recurrence_interval($schedules)
{
    $schedules['every_fifteen_minutes'] = array(
        'interval' => 900,
        'display' => __('Every 15 Minutes', 'textdomain')
    );

    return $schedules;
}
add_filter('cron_schedules', 'isa_add_cron_recurrence_interval');

// Add billing invoice email to fortnox order array
function fix_order_before_sending($fortnox_order, $order)
{
    $response = WF_Request::get('/customers/' . $fortnox_order['CustomerNumber']);

    if (empty($response->Customer->EmailInvoice)) {
        $invoiceEmail = get_post_meta($order->ID, '_billing_invoice_email');

        if (!empty($invoiceEmail)) {
            $result = WF_Request::put("/customers/" . $fortnox_order['CustomerNumber'], [
                'Customer' => ['EmailInvoice' => $invoiceEmail[0]],
            ]);
        }
    }

    $deliveryCompanyName = get_post_meta($order->ID, '_shipping_company');

    if (!empty($deliveryCompanyName)) {
        $fortnox_order['DeliveryName'] = $deliveryCompanyName[0];
    }

    $referenceFirstName = get_post_meta($order->ID, '_billing_first_name');
    $referenceLastName = get_post_meta($order->ID, '_billing_last_name');

    if (!empty($referenceFirstName[0]) && !empty($referenceLastName[0])) {
        $referencePerson = $referenceFirstName[0] . ' ' . $referenceLastName[0];

        if (!empty($referencePerson)) {
            $fortnox_order['YourReference'] = $referencePerson;
        }
    }

    return $fortnox_order;
}
add_action('wf_order_payload_before_create_or_update', 'fix_order_before_sending', 10, 2);

/**
 * Check if current user is admin and set all checkout fields to empty if so.
 *
 * @param  mixed  $input  The input for the field
 */
function if_admin_set_empty($input)
{
    if (current_user_can('administrator')) {
        return '';
    }

    return $input;
}
add_filter('woocommerce_checkout_get_value', 'if_admin_set_empty');

/**
 *  Change sale flash text
 */
function wc_custom_replace_sale_text($html)
{
    if (is_string($html) || is_array($html)) {
        return str_replace(__('Sale!', 'woocommerce'), __('Kampanj', 'woocommerce'), $html);
    }

    return $html;
}
add_filter('woocommerce_sale_flash', 'wc_custom_replace_sale_text');

/**
 * Alter the customer information before sending it away.
 *
 * @param  array  $customerArray  Array of default values
 * @return array                  The altered array
 */
function alter_customer_information_before_sending($customerArray)
{
    $organisationNumber = $customerArray['OrganisationNumber'];
    $customerByOrgNumber = null;

    // Check if there is an organisation number, if so, sync to that existing customer
    if (!empty($organisationNumber)) {
        $customerByOrgNumber = WF_Request::get("/customers?organisationnumber=" . $organisationNumber);
    }

    if ($customerByOrgNumber && !empty($customerByOrgNumber->Customers) && !empty($customerByOrgNumber->Customers[0])) {
        $customer = $customerByOrgNumber->Customers[0];

        $customerArray['Email'] = $customer->Email;
        $customerArray['Address1'] = $customer->Address1;
        $customerArray['Address2'] = $customer->Address2;
        $customerArray['City'] = $customer->City;
        $customerArray['ZipCode'] = $customer->ZipCode;
        $customerArray['Name'] = $customer->Name;
        $customerArray['OrganisationNumber'] = $customer->OrganisationNumber;
        $customerArray['Phone1'] = $customer->Phone;
        $customerArray['CustomerNumber'] = $customer->CustomerNumber;

        unset($customerArray['EmailInvoice']);
        unset($customerArray['Type']);
        unset($customerArray['CountryCode']);
        unset($customerArray['Currency']);
        unset($customerArray['ShowPriceVATIncluded']);
    } else {
        $filterByEmail = WF_Request::get("/customers?email=" . $customerArray['Email']);

        if (!empty($filterByEmail->Customers)) {
            $existingCustomer = $filterByEmail->Customers[0];
            $getFullCustomer = WF_Request::get("/customers/" . $existingCustomer->CustomerNumber);

            if (!empty($getFullCustomer->Customer)) {
                if ($getFullCustomer->Customer->EmailInvoice !== $getFullCustomer->Customer->Email) {
                    unset($customerArray['EmailInvoice']);
                } else {
                    $customerArray['EmailInvoice'] = null;
                }
            }
        } else {
            unset($customerArray['EmailInvoice']);
        }
    }

    return $customerArray;
}
add_filter('wf_customer_before_post', 'alter_customer_information_before_sending', 10, 1);



function filter_specific_products_from_indexing($shouldIndex, $post)
{
    if ($post->post_type == 'product') {
        $product = wc_get_product($post);

        if ($product->get_data()['virtual']) {
            return false;
        }
    }

    return $shouldIndex;
}
add_filter('algolia_should_index_post', 'filter_specific_products_from_indexing', 10, 2);

/**
 * Short code for displaying featured courses
 */
function featured_courses($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/page/course-featured', '');
    return ob_get_clean();
}
add_shortcode('featured-courses', 'featured_courses');

/**
 * Short codes for displaying all courses in a list
 */
function list_all_courses($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/page/course-list-all', '');
    return ob_get_clean();
}
add_shortcode('list-all-courses', 'list_all_courses');

/**
 * Short code for displaying all events in a category
 */
function list_events_in_category($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/list-events-in-category', '');
    return ob_get_clean();
}
add_shortcode('list-events-in-category', 'list_events_in_category');

/**
 * Short code for displaying events in multiple categories
 */
function list_events_in_multiple_categories($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/list-events-in-multiple-categories', '');
    return ob_get_clean();
}
add_shortcode('list-events-in-multiple-categories', 'list_events_in_multiple_categories');

/**
 * Short code for displaying social sharing links
 */
function social_sharing_links($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/post/post-share', '');
    return ob_get_clean();
}
add_shortcode('social-sharing-links', 'social_sharing_links');

/**
 * Short code for displaying usps
 */
function list_usps($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/usps', '');
    return ob_get_clean();
}
add_shortcode('list-usps', 'list_usps');

/**
 * Short code for related articles
 */
function list_related_articles($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/related-articles', '');
    return ob_get_clean();
}
add_shortcode('list-related-articles', 'list_related_articles');

/**
 * Short code for accordions
 */
function accordion($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/accordion', '');
    return ob_get_clean();
}
add_shortcode('accordion', 'accordion');

/**
 * Short code for documents
 */
function documents($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/documents', '');
    return ob_get_clean();
}
add_shortcode('documents', 'documents');

/**
 * Short code for links to pages
 */
function sitemap_pages($atts)
{
    ob_start();
    set_query_var('atts', $atts);
    get_template_part('template-parts/sitemap/sitemap-pages', '');
    return ob_get_clean();
}
add_shortcode('sitemap-pages', 'sitemap_pages');

function update_company_billing_number_in_admin($id, $order)
{
    if (is_admin()) {
        $orgNumber = get_post_meta($id, '_billing_company_number', true);

        if (!empty($_POST['_billing_company_number'])) {
            if ($_POST['_billing_company_number'] != $orgNumber) {
                update_post_meta($id, '_billing_company_number', $_POST['_billing_company_number']);
            }
        } else {
            update_post_meta($id, '_billing_company_number', '');
        }
    }
}
add_filter('woocommerce_update_order', 'update_company_billing_number_in_admin', 10, 2);

function add_sku_to_algolia_product_records($records, $post)
{
    $product = wc_get_product($post->ID);

    if ($product) {
        $data = [];

        foreach ($records as $record) {
            $articleNumbers = [];

            if ($product->is_type('variable')) {
                $articleNumbers = array_map(function ($variationId) {
                    return wc_get_product($variationId)->get_sku();
                }, $product->get_children());
            } else {
                $articleNumbers[] = $product->get_sku();
            }

            $record['sku'] = $articleNumbers;

            $data[] = $record;
        }

        return $data;
    }

    return $records;
}
add_filter('algolia_post_product_records', 'add_sku_to_algolia_product_records', 10, 2);

add_filter('vistrom_algolia_disable_archive_search', '__return_true');

add_filter('rank_math/snippet/rich_snippet_product_entity', function ($entity) {
    if (!is_product()) {
        return $entity;
    }

    $product = wc_get_product(get_the_ID());

    if (!$product->is_type('variable')) {
        $price_with_taxes = wc_get_price_including_tax($product);
        $entity['price'] = $price_with_taxes;

        return $entity;
    }

    $variations = $product->get_available_variations();

    $currentVariation = array_filter($variations, function ($var) use ($entity) {
        if (!empty($var['sku']) && !empty($entity['offers']['sku'])) {
            return ($var['sku'] == $entity['offers']['sku']);
        } else {
            false;
        }
    });

    if (!empty($currentVariation)) {
        $currentVariation = $currentVariation[0];

        $variation = wc_get_product($currentVariation['variation_id']);

        if ($variation) {
            $entity['offers']['price'] = (string) wc_get_price_including_tax($variation);
        }
    } elseif (!empty($variations)) {
        $offers = [];

        foreach ($variations as $variation) {
            $price_valid_until = get_post_meta($variation['variation_id'], '_sale_price_dates_to', true);

            $offer = [
                '@type' => 'Offer',
                'description' => strip_tags($variation['variation_description']),
                'price' => wc_get_price_including_tax(wc_get_product($variation['variation_id'])),
                'priceCurrency' => get_woocommerce_currency(),
                'availability' => $variation['is_in_stock'] ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                'itemCondition' => 'NewCondition',
                'priceValidUntil' => $price_valid_until ? date_i18n('Y-m-d', $price_valid_until) : '2025-12-31',
                'url' => $product->get_permalink(),
            ];

            if (!empty($variation['sku'])) {
                $offer['sku'] = $variation['sku'];
            } else {
                $offer['sku'] = '';
            }

            if (!empty($variation['gtin'])) {
                $offer['gtin'] = $variation['gtin'];
            } else {
                $offer['gtin'] = '';
            }

            $offers[] = $offer;
        }

        $entity['offers'] = $offers;
    }

    return $entity;
});

require get_template_directory() . '/includes/custom-shortcodes.php';
require get_template_directory() . '/includes/custom-blocks.php';
require get_template_directory() . '/includes/classes/BrandfastAjaxCustomerType.php';

// Override Vistrom Algolia JavaScript translations.
function filter_vistrom_algolia_translations_path()
{
    return get_template_directory() . '/algolia-search/vistrom-algolia-translations.php';
}
add_filter('vistrom_algolia_translations_path', 'filter_vistrom_algolia_translations_path');

// Enqueue custom Gutenberg editor styles and fonts.
function enqueue_gutenberg_editor_styles()
{
    wp_enqueue_style('fira-sans-editor', 'https://fonts.googleapis.com/css2?family=Fira+Sans:ital,wght@0,400;0,500;0,600;0,700;1,400;1,700&display=swap');
    wp_enqueue_style('custom-gutenberg-editor-styles', get_template_directory_uri() . '/style-editor.css');
}

add_action('enqueue_block_editor_assets', 'enqueue_gutenberg_editor_styles');

// Update WooCommerce product descriptions.
require get_theme_file_path('includes/brandfast-update-product-descriptions.php');

/*
|--------------------------------------------------------------------------
| Admin notices
|
| Remove undismissable admin notices caused by bugs in various plugins.
|--------------------------------------------------------------------------
*/
add_action('admin_notices', function () {
    // Rank Math.
    update_option('rank_math_registration_skip', 0);
}, 99);
