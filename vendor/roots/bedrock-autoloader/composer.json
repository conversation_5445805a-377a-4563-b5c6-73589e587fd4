{"name": "roots/bedrock-autoloader", "license": "MIT", "description": "An autoloader that enables standard plugins to be required just like must-use plugins", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/foxaii"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/swalkinshaw"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/austinpray"}], "keywords": ["autoloader", "bedrock", "mu-plugin", "must-use", "plugin", "wordpress"], "support": {"forum": "https://discourse.roots.io/"}, "require": {"php": ">=7.1"}, "autoload": {"psr-4": {"Roots\\Bedrock\\": "src/"}}, "require-dev": {"phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "10up/wp_mock": "^0.4.2"}}