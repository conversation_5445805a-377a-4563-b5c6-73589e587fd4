  
name: Tests

on: [push, pull_request]

jobs:
  run:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php: [7.1, 7.2, 7.3, 7.4]

    name: PHP ${{ matrix.php }}

    steps:
    - uses: actions/checkout@v2
    - uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php }}
        coverage: xdebug
    - run: composer validate
    - run: composer install --prefer-dist --no-interaction
    - run: vendor/bin/phpunit --coverage-text
