<?php
declare(strict_types=1);

require __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;
use Symfony\Component\HttpFoundation\Request;

// Load the environment variables.
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->safeLoad();

// Helper function to get environment variables
if (!function_exists('env')) {
    function env($key, $default = null) {
        $value = $_ENV[$key] ?? $_SERVER[$key] ?? $default;

        // Convert string representations of booleans
        if (is_string($value)) {
            switch (strtolower($value)) {
                case 'true':
                case '(true)':
                    return true;
                case 'false':
                case '(false)':
                    return false;
                case 'empty':
                case '(empty)':
                    return '';
                case 'null':
                case '(null)':
                    return null;
            }
        }

        return $value;
    }
}

// Set the environment type.
define('WP_ENVIRONMENT_TYPE', env('WP_ENVIRONMENT_TYPE', 'development'));

// Define cache to prevent WP Rocket from ruining the day.
define( 'WP_CACHE', true ); // Added by WP Rocket

// Set the default WordPress theme.
define('WP_DEFAULT_THEME', env('WP_DEFAULT_THEME', 'wordplate'));

// Set WordPress memory limit
define( 'WP_MEMORY_LIMIT', '256M' );
define( 'WP_MAX_MEMORY_LIMIT', '512M' );

// For developers: WordPress debugging mode.
$isDebugModeEnabled = env('WP_DEBUG', false);
define( 'WP_DEBUG', true );
define( 'WP_DEBUG_LOG', true );
define( 'WP_DEBUG_DISPLAY', false );
define( 'SCRIPT_DEBUG', true );

// The database configuration with database name, username, password,
// hostname charset and database collate type.
define('DB_NAME', "brandfast");
define('DB_USER', "root");
define('DB_PASSWORD', "");
define('DB_HOST', "127.0.0.1");
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// Set the unique authentication keys and salts.
define('AUTH_KEY', 'ffd89199bf90dadd308b548bfc3f1561ee670eb0e551534d095383c1f11c9502');
define('SECURE_AUTH_KEY', '700490f6a7cd5df7c3d1ea7b3bebd1938495bb2eb88959e89fdf87478bc9dca1');
define('LOGGED_IN_KEY', 'c572609b5ee05460749bb00442e1962d6b56d71798e4d7b7e5166cb0b3ebafaa');
define('NONCE_KEY', 'ba49a06bdc2d5d5ab94430d2ac3e76f93289b92884b9888f7fad83c554e20c66');
define('AUTH_SALT', '9245015f2deff4c99e72b220cc76dce3f1e2001b74dbb855e2b4b2a3c35f198e');
define('SECURE_AUTH_SALT', '67675d3507f4fb3f810efa808ff7053d0b9f02393b5c0f68d015ef2267406c89');
define('LOGGED_IN_SALT', '7e765041b0b632d1afcfac00f9b50ee2f77491fa1c4a8f419ff8176e9c6808c7');
define('NONCE_SALT', 'e79293eeee076a153decd92a45ef2f4a8fa6515a95f6954cd34af627d265f77d');

// Detect HTTPS behind a reverse proxy or a load balancer.
if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https') {
    $_SERVER['HTTPS'] = 'on';
}

// Set the home url to the current domain.
define( 'WP_HOME', 'https://brandfast.test' );

// Set the WordPress directory path.
define( 'WP_SITEURL', 'https://brandfast.test/wordpress' );

// Set the WordPress content directory path.
define( 'WP_CONTENT_DIR', '/Users/<USER>/Sites/brandfast' );
define( 'WP_CONTENT_URL', 'https://brandfast.test' );



// Disable WordPress auto updates.
define( 'AUTOMATIC_UPDATER_DISABLED', true );

// Disable WP-Cron (wp-cron.php) for faster performance.
define( 'DISABLE_WP_CRON', true );

// Prevent file edititing from the dashboard.
define( 'DISALLOW_FILE_EDIT', false );

// Disable plugin and theme updates and installation from the dashboard.
define( 'DISALLOW_FILE_MODS', false );

// Cleanup WordPress image edits.
define( 'IMAGE_EDIT_OVERWRITE', true );

// Disable technical issues emails.
// Limit the number of post revisions.
define( 'WP_POST_REVISIONS', 2 );

define('WP2FA_ENCRYPT_KEY', env('WP2FA_ENCRYPT_KEY', null));

// Set the database table prefix.
$table_prefix = 'wp_';

// Set the absolute path to the WordPress directory.
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/wordpress');
}

define( 'DUPLICATOR_AUTH_KEY', 'auiPaV<%8!Vgt$ eCHu-Q4jHOK&&.#J&A/gQgY?+RS-evH(-Fj@|I*Z^WJ,VS/oY' );
define( 'WP_PLUGIN_DIR', '/Users/<USER>/Sites/brandfast/plugins' );
define( 'WP_PLUGIN_URL', 'https://brandfast.test/plugins' );
define( 'WPMU_PLUGIN_DIR', '/Users/<USER>/Sites/brandfast/mu-plugins' );
define( 'WPMU_PLUGIN_URL', 'https://brandfast.test/mu-plugins' );
require_once ABSPATH . 'wp-settings.php';
